#!/usr/bin/env python3
"""
Simple web UI for Local Deep Researcher
"""

import asyncio
import json
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from src.ollama_deep_researcher.configuration import Configuration
from src.ollama_deep_researcher.graph import graph
from src.ollama_deep_researcher.state import SummaryStateInput

import streamlit as st

def main():
    st.set_page_config(
        page_title="Local Deep Researcher",
        page_icon="🔍",
        layout="wide"
    )
    
    st.title("🔍 Local Deep Researcher")
    st.markdown("*Powered by DuckDuckGo Search + Qwen/Qwen3-8B*")
    
    # Configuration display
    with st.expander("⚙️ Current Configuration", expanded=False):
        config = Configuration.from_runnable_config()
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**LLM Configuration:**")
            st.write(f"- Provider: `{config.llm_provider}`")
            st.write(f"- Model: `{config.local_llm}`")
            st.write(f"- Base URL: `{config.openai_base_url}`")
        
        with col2:
            st.write("**Search Configuration:**")
            st.write(f"- Search API: `{config.search_api}`")
            st.write(f"- Max Research Loops: `{config.max_web_research_loops}`")
            st.write(f"- Fetch Full Page: `{config.fetch_full_page}`")
    
    # Research input
    st.markdown("---")
    research_topic = st.text_area(
        "🎯 Enter your research topic:",
        placeholder="e.g., Latest developments in artificial intelligence, Climate change impacts on agriculture, etc.",
        height=100
    )
    
    # Research button
    if st.button("🚀 Start Research", type="primary", disabled=not research_topic.strip()):
        if research_topic.strip():
            run_research(research_topic.strip())

def run_research(topic: str):
    """Run the research process"""
    
    # Create progress indicators
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    # Create containers for results
    search_queries_container = st.container()
    sources_container = st.container()
    summary_container = st.container()
    
    try:
        status_text.text("🔄 Initializing research...")
        progress_bar.progress(10)
        
        # Create input state
        input_state = SummaryStateInput(research_topic=topic)
        
        # Configuration
        config = {
            "configurable": {
                "max_web_research_loops": 3,
                "search_api": "duckduckgo",
                "llm_provider": "openai_compatible",
                "fetch_full_page": True
            }
        }
        
        status_text.text("🔍 Starting research process...")
        progress_bar.progress(20)
        
        # Run the graph
        result = None
        step_count = 0
        max_steps = 10  # Prevent infinite loops
        
        search_queries = []
        all_sources = []
        
        for step_result in graph.stream(input_state, config=config):
            step_count += 1
            if step_count > max_steps:
                st.error("Research process exceeded maximum steps. Stopping.")
                break
                
            # Update progress
            progress = min(20 + (step_count * 70 // max_steps), 90)
            progress_bar.progress(progress)
            
            # Process each step
            for node_name, node_result in step_result.items():
                status_text.text(f"🔄 Processing: {node_name}")
                
                if node_name == "generate_query" and "search_query" in node_result:
                    search_queries.append(node_result["search_query"])
                    
                elif node_name == "web_research" and "sources_gathered" in node_result:
                    if node_result["sources_gathered"]:
                        all_sources.extend(node_result["sources_gathered"])
                
                # Store the final result
                result = node_result
        
        progress_bar.progress(100)
        status_text.text("✅ Research completed!")
        
        # Display results
        if search_queries:
            with search_queries_container:
                st.markdown("### 🔍 Search Queries Generated")
                for i, query in enumerate(search_queries, 1):
                    st.write(f"**Query {i}:** {query}")
        
        if all_sources:
            with sources_container:
                st.markdown("### 📚 Sources Found")
                unique_sources = []
                seen_urls = set()
                
                for source_group in all_sources:
                    if isinstance(source_group, list):
                        for source in source_group:
                            if isinstance(source, dict) and source.get('url') not in seen_urls:
                                unique_sources.append(source)
                                seen_urls.add(source.get('url'))
                
                for i, source in enumerate(unique_sources[:10], 1):  # Show top 10 sources
                    with st.expander(f"📄 Source {i}: {source.get('title', 'Unknown Title')[:60]}..."):
                        st.write(f"**URL:** {source.get('url', 'N/A')}")
                        st.write(f"**Content:** {source.get('content', 'N/A')[:300]}...")
        
        # Display final summary
        if result and "running_summary" in result:
            with summary_container:
                st.markdown("### 📝 Research Summary")
                st.markdown(result["running_summary"])
        else:
            st.warning("No final summary was generated. Please check the configuration and try again.")
            
    except Exception as e:
        st.error(f"An error occurred during research: {str(e)}")
        st.exception(e)
    
    finally:
        progress_bar.empty()
        status_text.empty()

if __name__ == "__main__":
    main()
