# Which search service to use, either 'duckduckgo', 'tavily', 'perplexity', Searxng
SEARCH_API='duckduckgo'
# For Searxng search, defaults to http://localhost:8888
SEARXNG_URL=

# Web Search API Keys (choose one or both)
TAVILY_API_KEY=tvly-xxxxx      # Get your key at https://tavily.com
PERPLEXITY_API_KEY=pplx-xxxxx  # Get your key at https://www.perplexity.ai

# LLM Configuration
LLM_PROVIDER=openai_compatible  # Options: ollama, lmstudio, openai_compatible
LOCAL_LLM=Qwen/Qwen3-8B        # Model name in LMStudio/Ollama/OpenAI-compatible API
LMSTUDIO_BASE_URL=http://localhost:1234/v1  # LMStudio OpenAI-compatible API URL
OLLAMA_BASE_URL=http://localhost:11434 # the endpoint of the Ollama service, defaults to http://localhost:11434 if not set

# OpenAI-compatible API Configuration
OPENAI_API_KEY=sk-iopewlrhdjifcztkffeqpkdskyicedpmpwtqsltysfvlpbee
OPENAI_BASE_URL=https://api.siliconflow.cn/v1

MAX_WEB_RESEARCH_LOOPS=3
FETCH_FULL_PAGE=True