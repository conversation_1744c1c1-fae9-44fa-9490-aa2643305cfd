# Which search service to use, either 'duckduckgo', 'tavily', 'perplexity', Searxng
SEARCH_API='duckduckgo'
# For Searxng search, defaults to http://localhost:8888
SEARXNG_URL=

# Web Search API Keys (choose one or both)
TAVILY_API_KEY=tvly-xxxxx      # Get your key at https://tavily.com
PERPLEXITY_API_KEY=pplx-xxxxx  # Get your key at https://www.perplexity.ai

# LLM Configuration
LLM_PROVIDER=lmstudio          # Options: ollama, lmstudio
LOCAL_LLM=qwen_qwq-32b         # Model name in LMStudio/Ollama
LMSTUDIO_BASE_URL=http://localhost:1234/v1  # LMStudio OpenAI-compatible API URL
OLLAMA_BASE_URL=http://localhost:11434 # the endpoint of the Ollama service, defaults to http://localhost:11434 if not set

MAX_WEB_RESEARCH_LOOPS=3
FETCH_FULL_PAGE=True