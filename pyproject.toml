[project]
name = "ollama-deep-researcher"
version = "0.0.1"
description = "Fully local web research and summarization assistant with <PERSON><PERSON><PERSON> and LangGraph."
authors = [
    { name = "<PERSON>" }
]
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.9"
dependencies = [
    "langgraph>=0.2.55",
    "langchain-community>=0.3.9",
    "tavily-python>=0.5.0",
    "langchain-ollama>=0.3.6",
    "duckduckgo-search>=7.3.0",
    "langchain-openai>=0.1.1",
    "openai>=1.12.0",
    "langchain_openai>=0.3.9",
    "httpx>=0.28.1",
    "markdownify>=0.11.0",
    "python-dotenv==1.0.1",
]

[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["ollama_deep_researcher"]

[tool.setuptools.package-dir]
"ollama_deep_researcher" = "src/ollama_deep_researcher"

[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    "UP035",
    "D417",
    "E501",
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[dependency-groups]
dev = [
    "ruff>=0.12.7",
]
