#!/usr/bin/env python3
"""
Simple test to verify the research assistant works
"""

import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from src.ollama_deep_researcher.configuration import Configuration
from src.ollama_deep_researcher.graph import generate_query, web_research, summarize_sources
from src.ollama_deep_researcher.state import SummaryState

def test_single_research_cycle():
    """Test a single research cycle"""
    
    topic = sys.argv[1] if len(sys.argv) > 1 else "Python programming best practices"
    
    print(f"🔍 Testing research on: {topic}")
    print("=" * 50)
    
    # Show configuration
    config = Configuration.from_runnable_config()
    print(f"📋 Configuration:")
    print(f"   - LLM Provider: {config.llm_provider}")
    print(f"   - Model: {config.local_llm}")
    print(f"   - Search API: {config.search_api}")
    print()
    
    try:
        # Create initial state
        state = SummaryState(
            research_topic=topic,
            search_query="",
            web_research_results=[],
            sources_gathered=[],
            running_summary="",
            research_loop_count=0
        )
        
        # Configuration for nodes
        node_config = {
            "configurable": {
                "max_web_research_loops": 2,
                "search_api": config.search_api,
                "llm_provider": config.llm_provider,
                "fetch_full_page": False,
                "use_tool_calling": False,
                "strip_thinking_tokens": True
            }
        }
        
        print("🔄 Step 1: Generating search query...")
        query_result = generate_query(state, node_config)
        print(f"   ✅ Generated query: {query_result.get('search_query', 'No query generated')}")
        
        # Update state
        state.search_query = query_result.get('search_query', '')
        
        if state.search_query:
            print("\n🔄 Step 2: Performing web research...")
            research_result = web_research(state, node_config)
            print(f"   ✅ Found {len(research_result.get('sources_gathered', []))} source groups")
            
            # Update state
            state.web_research_results = research_result.get('web_research_results', [])
            state.sources_gathered = research_result.get('sources_gathered', [])
            state.research_loop_count = research_result.get('research_loop_count', 1)
            
            if state.web_research_results:
                print("\n🔄 Step 3: Summarizing results...")
                summary_result = summarize_sources(state, node_config)
                summary = summary_result.get('running_summary', '')
                
                if summary:
                    print("   ✅ Summary generated successfully!")
                    print("\n📝 Research Summary:")
                    print("=" * 50)
                    print(summary)
                    print("=" * 50)
                else:
                    print("   ⚠️  No summary generated")
            else:
                print("   ⚠️  No research results to summarize")
        else:
            print("   ⚠️  No search query generated")
            
        # Show sources
        if state.sources_gathered:
            print(f"\n📚 Sources ({len(state.sources_gathered)} groups):")
            for i, source_group in enumerate(state.sources_gathered[:3], 1):
                if isinstance(source_group, list) and source_group:
                    for source in source_group[:2]:  # Show first 2 sources from each group
                        if isinstance(source, dict):
                            title = source.get('title', 'Unknown Title')[:50]
                            url = source.get('url', 'N/A')
                            print(f"   {i}. {title}...")
                            print(f"      {url}")
                            break
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_research_cycle()
