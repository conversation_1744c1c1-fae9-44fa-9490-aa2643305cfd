#!/usr/bin/env python3
"""
Custom Web UI for Local Deep Researcher with node visualization
"""

import streamlit as st
import json
import time
from typing import Dict, Any, List
from dotenv import load_dotenv
import graphviz

# Load environment variables
load_dotenv()

from src.ollama_deep_researcher.configuration import Configuration
from src.ollama_deep_researcher.graph import graph
from src.ollama_deep_researcher.state import SummaryStateInput

def main():
    st.set_page_config(
        page_title="本地深度研究助手",
        page_icon="🔍",
        layout="wide"
    )

    # Header
    st.title("🔍 本地深度研究助手")
    st.markdown("*基于 DuckDuckGo 搜索 + Qwen/Qwen3-8B 模型*")
    
    # Sidebar with configuration
    with st.sidebar:
        st.header("⚙️ 系统配置")
        config = Configuration.from_runnable_config()

        st.write("**AI模型设置:**")
        st.write(f"- 提供商: `{config.llm_provider}`")
        st.write(f"- 模型: `{config.local_llm}`")
        st.write(f"- API地址: `{config.openai_base_url}`")

        st.write("**搜索设置:**")
        st.write(f"- 搜索引擎: `{config.search_api}`")
        st.write(f"- 最大循环: `{config.max_web_research_loops}`")
        st.write(f"- 完整页面: `{config.fetch_full_page}`")
        
        st.markdown("---")
        st.write("**研究流程图:**")

        # Create a simple flow diagram
        graph = graphviz.Digraph()
        graph.attr(rankdir='TB', size='6,8')
        graph.attr('node', shape='box', style='rounded,filled', fillcolor='lightblue')

        # Add nodes
        graph.node('start', '开始', fillcolor='lightgreen')
        graph.node('generate_query', '🎯 生成\n搜索查询')
        graph.node('web_research', '🔍 网络\n搜索')
        graph.node('summarize', '📝 总结\n资源')
        graph.node('reflect', '🤔 反思\n总结')
        graph.node('should_continue', '继续研究?', shape='diamond', fillcolor='yellow')
        graph.node('finalize', '✅ 完成\n总结', fillcolor='lightcoral')
        graph.node('end', '结束', fillcolor='lightcoral')

        # Add edges
        graph.edge('start', 'generate_query')
        graph.edge('generate_query', 'web_research')
        graph.edge('web_research', 'summarize')
        graph.edge('summarize', 'reflect')
        graph.edge('reflect', 'should_continue')
        graph.edge('should_continue', 'generate_query', label='是')
        graph.edge('should_continue', 'finalize', label='否')
        graph.edge('finalize', 'end')

        st.graphviz_chart(graph.source)
    
    # Main content
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.header("📝 研究输入")
        research_topic = st.text_area(
            "请输入您的研究主题:",
            placeholder="例如: 人工智能最新发展、气候变化对农业的影响等",
            height=100
        )

        # Advanced settings
        with st.expander("🔧 高级设置"):
            max_loops = st.slider("最大研究循环次数", 1, 5, 2)
            fetch_full_page = st.checkbox("获取完整页面内容", value=False)
            use_tool_calling = st.checkbox("使用工具调用", value=False)

        start_research = st.button("🚀 开始研究", type="primary", disabled=not research_topic.strip())

    with col2:
        st.header("📊 研究进度")
        
        if start_research and research_topic.strip():
            run_research_with_visualization(research_topic.strip(), max_loops, fetch_full_page, use_tool_calling)

def run_research_with_visualization(topic: str, max_loops: int, fetch_full_page: bool, use_tool_calling: bool):
    """Run research with real-time visualization"""
    
    # Create containers for different sections
    progress_container = st.container()
    nodes_container = st.container()
    results_container = st.container()
    
    with progress_container:
        st.subheader("🔄 当前进度")
        progress_bar = st.progress(0)
        status_text = st.empty()

    with nodes_container:
        st.subheader("🎯 节点执行流程")
        node_status = st.empty()
    
    # Initialize tracking variables
    node_states = {
        "generate_query": {"status": "pending", "result": None},
        "web_research": {"status": "pending", "result": None},
        "summarize_sources": {"status": "pending", "result": None},
        "reflect_on_summary": {"status": "pending", "result": None},
        "finalize_summary": {"status": "pending", "result": None}
    }
    
    search_queries = []
    all_sources = []
    summaries = []
    
    try:
        # Create input state
        input_state = SummaryStateInput(research_topic=topic)
        
        # Configuration
        config = {
            "configurable": {
                "max_web_research_loops": max_loops,
                "search_api": "duckduckgo",
                "llm_provider": "openai_compatible",
                "fetch_full_page": fetch_full_page,
                "use_tool_calling": use_tool_calling,
                "strip_thinking_tokens": True
            }
        }
        
        status_text.text("🚀 开始研究流程...")
        progress_bar.progress(10)
        
        # Update node status display
        update_node_status_display(node_status, node_states)
        
        step_count = 0
        max_steps = 15
        
        # Run the graph
        for step_result in graph.stream(input_state, config=config):
            step_count += 1
            if step_count > max_steps:
                status_text.text("⚠️ 研究流程超过最大步数限制")
                break

            # Update progress
            progress = min(10 + (step_count * 80 // max_steps), 90)
            progress_bar.progress(progress)

            # Process each step
            for node_name, node_result in step_result.items():
                status_text.text(f"🔄 正在处理: {node_name}")
                
                # Update node status
                if node_name in node_states:
                    node_states[node_name]["status"] = "running"
                    update_node_status_display(node_status, node_states)
                    time.sleep(0.5)  # Brief pause for visualization
                
                # Process specific node results
                if node_name == "generate_query" and "search_query" in node_result:
                    query = node_result["search_query"]
                    search_queries.append(query)
                    node_states[node_name]["result"] = f"查询: {query}"
                    node_states[node_name]["status"] = "completed"

                elif node_name == "web_research":
                    if "sources_gathered" in node_result and node_result["sources_gathered"]:
                        sources = node_result["sources_gathered"]
                        all_sources.extend(sources)
                        node_states[node_name]["result"] = f"找到 {len(sources)} 个资源组"
                        node_states[node_name]["status"] = "completed"

                elif node_name == "summarize_sources":
                    if "running_summary" in node_result:
                        summary = node_result["running_summary"]
                        summaries.append(summary)
                        node_states[node_name]["result"] = f"总结已生成 ({len(summary)} 字符)"
                        node_states[node_name]["status"] = "completed"

                elif node_name == "reflect_on_summary":
                    if "search_query" in node_result:
                        new_query = node_result["search_query"]
                        node_states[node_name]["result"] = f"新查询: {new_query}"
                        node_states[node_name]["status"] = "completed"

                elif node_name == "finalize_summary":
                    node_states[node_name]["result"] = "总结已完成"
                    node_states[node_name]["status"] = "completed"
                
                # Update display
                update_node_status_display(node_status, node_states)
        
        progress_bar.progress(100)
        status_text.text("✅ 研究完成!")
        
        # Display results
        with results_container:
            display_results(search_queries, all_sources, summaries, step_result if 'step_result' in locals() else None)
            
    except Exception as e:
        status_text.text(f"❌ Error: {str(e)}")
        st.exception(e)

def update_node_status_display(container, node_states: Dict):
    """Update the node status display"""
    with container:
        cols = st.columns(5)
        
        node_info = [
            ("generate_query", "🎯 生成查询"),
            ("web_research", "🔍 网络搜索"),
            ("summarize_sources", "📝 总结资源"),
            ("reflect_on_summary", "🤔 反思总结"),
            ("finalize_summary", "✅ 完成总结")
        ]
        
        for i, (node_key, node_label) in enumerate(node_info):
            with cols[i]:
                status = node_states[node_key]["status"]
                result = node_states[node_key]["result"]
                
                if status == "completed":
                    st.success(node_label)
                    if result:
                        st.caption(result)
                elif status == "running":
                    st.warning(f"{node_label} ⏳")
                else:
                    st.info(node_label)

def display_results(search_queries: List[str], all_sources: List, summaries: List[str], final_result: Dict):
    """Display the research results"""
    
    st.subheader("📋 研究结果")

    # Tabs for different result types
    tab1, tab2, tab3, tab4 = st.tabs(["🔍 搜索查询", "📚 资源来源", "📝 总结内容", "🎯 最终结果"])

    with tab1:
        st.write("**生成的搜索查询:**")
        for i, query in enumerate(search_queries, 1):
            st.write(f"{i}. {query}")

    with tab2:
        st.write("**找到的资源:**")
        unique_sources = []
        seen_urls = set()
        
        for source_group in all_sources:
            if isinstance(source_group, list):
                for source in source_group:
                    if isinstance(source, dict) and source.get('url') not in seen_urls:
                        unique_sources.append(source)
                        seen_urls.add(source.get('url'))
        
        for i, source in enumerate(unique_sources[:10], 1):
            with st.expander(f"📄 {i}. {source.get('title', 'Unknown')[:50]}..."):
                st.write(f"**URL:** {source.get('url', 'N/A')}")
                st.write(f"**Content:** {source.get('content', 'N/A')[:300]}...")
    
    with tab3:
        st.write("**Generated Summaries:**")
        for i, summary in enumerate(summaries, 1):
            with st.expander(f"📝 Summary {i}"):
                st.markdown(summary)
    
    with tab4:
        if final_result and "running_summary" in final_result:
            st.write("**Final Research Summary:**")
            st.markdown(final_result["running_summary"])
        else:
            st.warning("No final summary available")

if __name__ == "__main__":
    main()
