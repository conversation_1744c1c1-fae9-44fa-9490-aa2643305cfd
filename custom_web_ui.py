#!/usr/bin/env python3
"""
Custom Web UI for Local Deep Researcher with node visualization
"""

import streamlit as st
import json
import time
from typing import Dict, Any, List
from dotenv import load_dotenv
import graphviz

# Load environment variables
load_dotenv()

from src.ollama_deep_researcher.configuration import Configuration
from src.ollama_deep_researcher.graph import graph
from src.ollama_deep_researcher.state import SummaryStateInput

def main():
    st.set_page_config(
        page_title="Local Deep Researcher",
        page_icon="🔍",
        layout="wide"
    )
    
    # Header
    st.title("🔍 Local Deep Researcher")
    st.markdown("*Powered by DuckDuckGo Search + Qwen/Qwen3-8B*")
    
    # Sidebar with configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        config = Configuration.from_runnable_config()
        
        st.write("**LLM Settings:**")
        st.write(f"- Provider: `{config.llm_provider}`")
        st.write(f"- Model: `{config.local_llm}`")
        st.write(f"- Base URL: `{config.openai_base_url}`")
        
        st.write("**Search Settings:**")
        st.write(f"- API: `{config.search_api}`")
        st.write(f"- Max Loops: `{config.max_web_research_loops}`")
        st.write(f"- Full Page: `{config.fetch_full_page}`")
        
        st.markdown("---")
        st.write("**Graph Flow:**")

        # Create a simple flow diagram
        graph = graphviz.Digraph()
        graph.attr(rankdir='TB', size='6,8')
        graph.attr('node', shape='box', style='rounded,filled', fillcolor='lightblue')

        # Add nodes
        graph.node('start', 'START', fillcolor='lightgreen')
        graph.node('generate_query', '🎯 Generate\nQuery')
        graph.node('web_research', '🔍 Web\nResearch')
        graph.node('summarize', '📝 Summarize\nSources')
        graph.node('reflect', '🤔 Reflect on\nSummary')
        graph.node('should_continue', 'Continue?', shape='diamond', fillcolor='yellow')
        graph.node('finalize', '✅ Finalize\nSummary', fillcolor='lightcoral')
        graph.node('end', 'END', fillcolor='lightcoral')

        # Add edges
        graph.edge('start', 'generate_query')
        graph.edge('generate_query', 'web_research')
        graph.edge('web_research', 'summarize')
        graph.edge('summarize', 'reflect')
        graph.edge('reflect', 'should_continue')
        graph.edge('should_continue', 'generate_query', label='Yes')
        graph.edge('should_continue', 'finalize', label='No')
        graph.edge('finalize', 'end')

        st.graphviz_chart(graph.source)
    
    # Main content
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.header("📝 Research Input")
        research_topic = st.text_area(
            "Enter your research topic:",
            placeholder="e.g., Latest developments in artificial intelligence",
            height=100
        )
        
        # Advanced settings
        with st.expander("🔧 Advanced Settings"):
            max_loops = st.slider("Max Research Loops", 1, 5, 2)
            fetch_full_page = st.checkbox("Fetch Full Page Content", value=False)
            use_tool_calling = st.checkbox("Use Tool Calling", value=False)
        
        start_research = st.button("🚀 Start Research", type="primary", disabled=not research_topic.strip())
    
    with col2:
        st.header("📊 Research Progress")
        
        if start_research and research_topic.strip():
            run_research_with_visualization(research_topic.strip(), max_loops, fetch_full_page, use_tool_calling)

def run_research_with_visualization(topic: str, max_loops: int, fetch_full_page: bool, use_tool_calling: bool):
    """Run research with real-time visualization"""
    
    # Create containers for different sections
    progress_container = st.container()
    nodes_container = st.container()
    results_container = st.container()
    
    with progress_container:
        st.subheader("🔄 Current Progress")
        progress_bar = st.progress(0)
        status_text = st.empty()
    
    with nodes_container:
        st.subheader("🎯 Node Execution Flow")
        node_status = st.empty()
    
    # Initialize tracking variables
    node_states = {
        "generate_query": {"status": "pending", "result": None},
        "web_research": {"status": "pending", "result": None},
        "summarize_sources": {"status": "pending", "result": None},
        "reflect_on_summary": {"status": "pending", "result": None},
        "finalize_summary": {"status": "pending", "result": None}
    }
    
    search_queries = []
    all_sources = []
    summaries = []
    
    try:
        # Create input state
        input_state = SummaryStateInput(research_topic=topic)
        
        # Configuration
        config = {
            "configurable": {
                "max_web_research_loops": max_loops,
                "search_api": "duckduckgo",
                "llm_provider": "openai_compatible",
                "fetch_full_page": fetch_full_page,
                "use_tool_calling": use_tool_calling,
                "strip_thinking_tokens": True
            }
        }
        
        status_text.text("🚀 Starting research process...")
        progress_bar.progress(10)
        
        # Update node status display
        update_node_status_display(node_status, node_states)
        
        step_count = 0
        max_steps = 15
        
        # Run the graph
        for step_result in graph.stream(input_state, config=config):
            step_count += 1
            if step_count > max_steps:
                status_text.text("⚠️ Research process exceeded maximum steps")
                break
            
            # Update progress
            progress = min(10 + (step_count * 80 // max_steps), 90)
            progress_bar.progress(progress)
            
            # Process each step
            for node_name, node_result in step_result.items():
                status_text.text(f"🔄 Processing: {node_name}")
                
                # Update node status
                if node_name in node_states:
                    node_states[node_name]["status"] = "running"
                    update_node_status_display(node_status, node_states)
                    time.sleep(0.5)  # Brief pause for visualization
                
                # Process specific node results
                if node_name == "generate_query" and "search_query" in node_result:
                    query = node_result["search_query"]
                    search_queries.append(query)
                    node_states[node_name]["result"] = f"Query: {query}"
                    node_states[node_name]["status"] = "completed"
                    
                elif node_name == "web_research":
                    if "sources_gathered" in node_result and node_result["sources_gathered"]:
                        sources = node_result["sources_gathered"]
                        all_sources.extend(sources)
                        node_states[node_name]["result"] = f"Found {len(sources)} source groups"
                        node_states[node_name]["status"] = "completed"
                
                elif node_name == "summarize_sources":
                    if "running_summary" in node_result:
                        summary = node_result["running_summary"]
                        summaries.append(summary)
                        node_states[node_name]["result"] = f"Summary generated ({len(summary)} chars)"
                        node_states[node_name]["status"] = "completed"
                
                elif node_name == "reflect_on_summary":
                    if "search_query" in node_result:
                        new_query = node_result["search_query"]
                        node_states[node_name]["result"] = f"New query: {new_query}"
                        node_states[node_name]["status"] = "completed"
                
                elif node_name == "finalize_summary":
                    node_states[node_name]["result"] = "Summary finalized"
                    node_states[node_name]["status"] = "completed"
                
                # Update display
                update_node_status_display(node_status, node_states)
        
        progress_bar.progress(100)
        status_text.text("✅ Research completed!")
        
        # Display results
        with results_container:
            display_results(search_queries, all_sources, summaries, step_result if 'step_result' in locals() else None)
            
    except Exception as e:
        status_text.text(f"❌ Error: {str(e)}")
        st.exception(e)

def update_node_status_display(container, node_states: Dict):
    """Update the node status display"""
    with container:
        cols = st.columns(5)
        
        node_info = [
            ("generate_query", "🎯 Generate Query"),
            ("web_research", "🔍 Web Research"),
            ("summarize_sources", "📝 Summarize"),
            ("reflect_on_summary", "🤔 Reflect"),
            ("finalize_summary", "✅ Finalize")
        ]
        
        for i, (node_key, node_label) in enumerate(node_info):
            with cols[i]:
                status = node_states[node_key]["status"]
                result = node_states[node_key]["result"]
                
                if status == "completed":
                    st.success(node_label)
                    if result:
                        st.caption(result)
                elif status == "running":
                    st.warning(f"{node_label} ⏳")
                else:
                    st.info(node_label)

def display_results(search_queries: List[str], all_sources: List, summaries: List[str], final_result: Dict):
    """Display the research results"""
    
    st.subheader("📋 Research Results")
    
    # Tabs for different result types
    tab1, tab2, tab3, tab4 = st.tabs(["🔍 Queries", "📚 Sources", "📝 Summaries", "🎯 Final Result"])
    
    with tab1:
        st.write("**Generated Search Queries:**")
        for i, query in enumerate(search_queries, 1):
            st.write(f"{i}. {query}")
    
    with tab2:
        st.write("**Sources Found:**")
        unique_sources = []
        seen_urls = set()
        
        for source_group in all_sources:
            if isinstance(source_group, list):
                for source in source_group:
                    if isinstance(source, dict) and source.get('url') not in seen_urls:
                        unique_sources.append(source)
                        seen_urls.add(source.get('url'))
        
        for i, source in enumerate(unique_sources[:10], 1):
            with st.expander(f"📄 {i}. {source.get('title', 'Unknown')[:50]}..."):
                st.write(f"**URL:** {source.get('url', 'N/A')}")
                st.write(f"**Content:** {source.get('content', 'N/A')[:300]}...")
    
    with tab3:
        st.write("**Generated Summaries:**")
        for i, summary in enumerate(summaries, 1):
            with st.expander(f"📝 Summary {i}"):
                st.markdown(summary)
    
    with tab4:
        if final_result and "running_summary" in final_result:
            st.write("**Final Research Summary:**")
            st.markdown(final_result["running_summary"])
        else:
            st.warning("No final summary available")

if __name__ == "__main__":
    main()
