#!/usr/bin/env python3
"""
Test script to verify the configuration and basic functionality.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from src.ollama_deep_researcher.configuration import Configuration
from src.ollama_deep_researcher.graph import get_llm
from src.ollama_deep_researcher.utils import duckduckgo_search

def test_configuration():
    """Test configuration loading."""
    print("=== Testing Configuration ===")
    config = Configuration.from_runnable_config()
    
    print(f"✓ LLM Provider: {config.llm_provider}")
    print(f"✓ Model: {config.local_llm}")
    print(f"✓ Search API: {config.search_api}")
    print(f"✓ OpenAI Base URL: {config.openai_base_url}")
    print(f"✓ OpenAI API Key: {config.openai_api_key[:20]}...")
    print()

def test_search():
    """Test search functionality."""
    print("=== Testing Search ===")
    try:
        results = duckduckgo_search("Python programming", max_results=2)
        print(f"✓ Search successful! Found {len(results['results'])} results")
        for i, result in enumerate(results['results'][:2]):
            print(f"  {i+1}. {result['title'][:50]}...")
    except Exception as e:
        print(f"✗ Search failed: {e}")
    print()

def test_llm_initialization():
    """Test LLM initialization."""
    print("=== Testing LLM Initialization ===")
    try:
        config = Configuration.from_runnable_config()
        llm = get_llm(config)
        print(f"✓ LLM initialized successfully: {type(llm).__name__}")
        print(f"  - Base URL: {getattr(llm, 'base_url', 'N/A')}")
        print(f"  - Model: {getattr(llm, 'model_name', getattr(llm, 'model', 'N/A'))}")
    except Exception as e:
        print(f"✗ LLM initialization failed: {e}")
    print()

if __name__ == "__main__":
    print("🧪 Testing Local Deep Researcher Configuration\n")
    
    test_configuration()
    test_search()
    test_llm_initialization()
    
    print("🎉 All tests completed!")
