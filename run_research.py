#!/usr/bin/env python3
"""
Simple command-line interface for Local Deep Researcher
"""

import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from src.ollama_deep_researcher.configuration import Configuration
from src.ollama_deep_researcher.graph import graph
from src.ollama_deep_researcher.state import SummaryStateInput

def main():
    if len(sys.argv) < 2:
        print("🔍 Local Deep Researcher")
        print("Usage: python run_research.py \"Your research topic\"")
        print("\nExample:")
        print("  python run_research.py \"Latest developments in artificial intelligence\"")
        return
    
    topic = " ".join(sys.argv[1:])
    print(f"🔍 Starting research on: {topic}")
    print("=" * 60)
    
    # Show configuration
    config = Configuration.from_runnable_config()
    print(f"📋 Configuration:")
    print(f"   - LLM Provider: {config.llm_provider}")
    print(f"   - Model: {config.local_llm}")
    print(f"   - Search API: {config.search_api}")
    print(f"   - Max Research Loops: {config.max_web_research_loops}")
    print()
    
    try:
        # Create input state
        input_state = SummaryStateInput(research_topic=topic)
        
        # Configuration for the graph
        graph_config = {
            "configurable": {
                "max_web_research_loops": config.max_web_research_loops,
                "search_api": config.search_api,
                "llm_provider": config.llm_provider,
                "fetch_full_page": config.fetch_full_page
            }
        }
        
        print("🚀 Starting research process...")
        print()
        
        search_queries = []
        all_sources = []
        step_count = 0
        max_steps = 15  # Prevent infinite loops
        
        # Run the graph and process results
        for step_result in graph.stream(input_state, config=graph_config):
            step_count += 1
            if step_count > max_steps:
                print("⚠️  Research process exceeded maximum steps. Stopping.")
                break
            
            # Process each step
            for node_name, node_result in step_result.items():
                print(f"🔄 Processing: {node_name}")
                
                if node_name == "generate_query" and "search_query" in node_result:
                    query = node_result["search_query"]
                    search_queries.append(query)
                    print(f"   🔍 Generated search query: {query}")
                    
                elif node_name == "web_research":
                    if "sources_gathered" in node_result and node_result["sources_gathered"]:
                        sources = node_result["sources_gathered"]
                        all_sources.extend(sources)
                        print(f"   📚 Found {len(sources)} source groups")
                        
                elif node_name == "summarize":
                    if "running_summary" in node_result:
                        print(f"   📝 Updated summary (length: {len(node_result['running_summary'])} chars)")
                        
                elif node_name == "reflect_on_summary":
                    if "search_query" in node_result:
                        print(f"   🤔 Reflection generated new query: {node_result['search_query']}")
        
        print()
        print("=" * 60)
        print("✅ Research completed!")
        print()
        
        # Display search queries
        if search_queries:
            print("🔍 Search Queries Generated:")
            for i, query in enumerate(search_queries, 1):
                print(f"   {i}. {query}")
            print()
        
        # Display sources summary
        if all_sources:
            unique_sources = []
            seen_urls = set()
            
            for source_group in all_sources:
                if isinstance(source_group, list):
                    for source in source_group:
                        if isinstance(source, dict) and source.get('url') not in seen_urls:
                            unique_sources.append(source)
                            seen_urls.add(source.get('url'))
            
            print(f"📚 Sources Found ({len(unique_sources)} unique sources):")
            for i, source in enumerate(unique_sources[:5], 1):  # Show top 5 sources
                print(f"   {i}. {source.get('title', 'Unknown Title')[:60]}...")
                print(f"      URL: {source.get('url', 'N/A')}")
            
            if len(unique_sources) > 5:
                print(f"   ... and {len(unique_sources) - 5} more sources")
            print()
        
        # Get final result from the last step
        final_result = None
        for step_result in [step_result]:  # Get the last step_result
            for node_name, node_result in step_result.items():
                if "running_summary" in node_result:
                    final_result = node_result
                    break
        
        # Display final summary
        if final_result and "running_summary" in final_result:
            print("📝 Final Research Summary:")
            print("=" * 60)
            print(final_result["running_summary"])
            print("=" * 60)
        else:
            print("⚠️  No final summary was generated. The research may have been incomplete.")
            
    except Exception as e:
        print(f"❌ An error occurred during research: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
